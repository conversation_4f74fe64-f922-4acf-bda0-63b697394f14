# 需求 [divide_disk_download]

## 反馈

1. Fred反馈图片下载改用分盘下载保存
2. 这次修改把addqueue/needanalyze去掉一个，然后下载速度也统计一下，比如70线程，每个1M/s，应该显示70M/s。这个写盘的统计有些问题，也订正一下吧。

## 需求提出人:   Fred/Maggie

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2025-08-01

## 原因

1. 目前是服务器下载图片后保存到固态盘和挂载盘,但是写挂载盘很慢,改用两个服务器分别执行下载任务,一个写挂载盘,一个写固态盘

## 解决办法

1. 两台服务器各自有一份自己的`queue collection`。eg: `reso_photo_download_queue_ca6`, `reso_photo_download_queue_ca7`
2. 配置文件添加一个参数开关(eg: `isWatch`),当开关打开时watch rni的房源表
3. 在监听到数据变化更新`queue`表时, 根据房源信息计算`phoP` 并分别写入两个`queue collection`中。
4. 在图片下载完成更新`merged`表时, 获取房源信息计算`phoLH`,`docLH`,`tnLH`,与需要更新的信息比较
  1. 数据不一致时,将信息写入到中间表 ``
  2. 数据一致时,更新merged表并删除中间表的相关信息
5. 统计log信息`addedToQueue/needAnalyze`去掉一个,下载信息需要订正

## 是否需要补充UT

1. 需要补充并修复UT

## 确认日期:    2025-08

## online-step
